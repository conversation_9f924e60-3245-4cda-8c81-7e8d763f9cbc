package com.app.messej.ui.home.businesstab

import android.content.Context
import android.content.res.ColorStateList
import android.graphics.Typeface
import android.os.Bundle
import android.text.BidiFormatter
import android.text.Editable
import android.text.TextWatcher
import android.text.style.StyleSpan
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ArrayAdapter
import android.widget.RadioButton
import android.widget.RadioGroup
import android.widget.TextView
import android.widget.Toast
import androidx.appcompat.widget.AppCompatTextView
import androidx.core.content.ContextCompat
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.activityViewModels
import androidx.fragment.app.setFragmentResultListener

import com.app.messej.ui.home.businesstab.PayoutOtpFragment.Companion.PHONE_VERIFICATION_REQUEST_KEY
import com.app.messej.ui.home.businesstab.PayoutOtpFragment.Companion.PHONE_VERIFICATION_RESULT_KEY
import androidx.fragment.app.viewModels
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import com.app.messej.MainActivity
import com.app.messej.R
import com.app.messej.data.model.enums.InputType
import com.app.messej.data.model.enums.OtpContext
import com.app.messej.data.model.enums.PayoutFormErrorType
import com.app.messej.data.utils.UserInfoUtil
import com.app.messej.databinding.FragmentDynamicFormBinding
import com.app.messej.databinding.LayoutFormFileFieldBinding
import com.app.messej.databinding.LayoutFormPhoneFieldBinding
import com.app.messej.databinding.LayoutFormRadioFieldBinding
import com.app.messej.databinding.LayoutFormSpinnerFieldBinding
import com.app.messej.databinding.LayoutFormTextFieldBinding
import com.app.messej.ui.home.businesstab.operations.tasks.review.BusinessWithDrawViewModel
import com.app.messej.ui.home.publictab.common.BaseProfilePicAttachFragment
import com.app.messej.ui.home.publictab.huddles.create.CreateHuddleFragmentDirections
import com.app.messej.ui.profile.ProfileImageAttachSourceFragment
import com.app.messej.ui.utils.BindingAdapters.loadImage
import com.app.messej.ui.utils.NavigationExtensions.navigateSafe
import com.app.messej.ui.utils.TextFormatUtils.highlightOccurrences
import com.google.android.material.textfield.TextInputLayout
import com.skydoves.balloon.ArrowPositionRules
import com.skydoves.balloon.Balloon
import com.skydoves.balloon.BalloonAnimation
import com.skydoves.balloon.BalloonSizeSpec
import com.skydoves.balloon.createBalloon


class DynamicFormFragment : BaseProfilePicAttachFragment() {

    private lateinit var binding: FragmentDynamicFormBinding
    private val businessWithDrawviewModel: BusinessWithDrawViewModel by activityViewModels()
    override val viewModel: PaymentMethodViewModel by viewModels()
    private val inflater by lazy { LayoutInflater.from(requireContext()) }

    override val shouldCropImage: Boolean = false

    override val bindingRoot: View
        get() = binding.root

    private val args: DynamicFormFragmentArgs by navArgs()

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View {
        binding = DataBindingUtil.inflate(
            inflater, R.layout.fragment_dynamic_form, container, false
        )
        binding.lifecycleOwner = viewLifecycleOwner
        binding.viewModel = viewModel
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setup()
        observe()
    }

    override fun onStart() {
        super.onStart()
        (activity as MainActivity).setupActionBar(binding.customActionBar.toolbar)
        binding.customActionBar.toolBarTitle.text = args.paymentMethod.toString()
    }

    private fun setup() {
        val titleText = getString(R.string.business_official_information_title_note)
        val text=String.format( getString(R.string.business_payout_note),  BidiFormatter.getInstance().unicodeWrap(titleText))
        binding.informationLabel.text = text.highlightOccurrences(titleText){ StyleSpan(Typeface.BOLD) }

        // Use the payment method ID from navigation arguments
        val paymentMethodId = if (args.paymentMethodId != -1) args.paymentMethodId else null
        viewModel.getPaymentMethodFormData(paymentMethodId)
        viewModel.getPaymentMethodForm(paymentMethodId)

        binding.btnNext.setOnClickListener {
            if (viewModel.validateFields()) {
                val payoutId = businessWithDrawviewModel._backEndDbID.value
                if (payoutId != null) {
                    viewModel.submitPaymentMethodForm(payoutId,false)
                }
            } else {
                Toast.makeText(requireContext(), getString(R.string.business_payot_fill_all_fields), Toast.LENGTH_SHORT).show()
            }
        }
        binding.btnCancel.setOnClickListener {
            findNavController().popBackStack()
        }
    }


    private fun observe() {
        viewModel.errorLiveData.observe(viewLifecycleOwner) {
            Toast.makeText(requireContext(), it, Toast.LENGTH_SHORT).show()
        }
        viewModel.phoneCountryCode.observe(viewLifecycleOwner) {}

        viewModel.paymentMethodSubmitSuccess.observe(viewLifecycleOwner) { isSuccess->
            val payoutDate = viewModel.paymentFormResponse.value?.payoutDate
            val paymentMethod = viewModel.paymentFormResponse.value?.paymentMethod
                if (!isSuccess) {
                    val phoneField = viewModel._fieldDataList.find { it.inputType == InputType.PHONE }
                    val phoneCountryCode = viewModel.phoneCountryCode.value ?: ""
                    val phoneNumber = phoneField?.value?.substringAfter('-')?:return@observe
                    val action = DynamicFormFragmentDirections.actionDynamicFormFragmentToPayoutOtpFragment(
                        phoneCountryCode = phoneCountryCode,
                        phoneNumber = phoneNumber,
                        otpContext = OtpContext.PAYMENT_METHOD
                    )
                    findNavController().navigateSafe(action)
                } else {
                    findNavController().navigateSafe(DynamicFormFragmentDirections.actionDynamicFormFragmentToSuccessSellFlixAlertFragment(
                        payoutDate = payoutDate ?: "",
                        paymentMethod = paymentMethod ?: ""
                    ))
                }
        }

        viewModel.validationResult.observe(viewLifecycleOwner) { validationResult ->
            validationResult.let { (viewId, errorMessage) ->
                showError(viewId, errorMessage)
            }
        }

        setFragmentResultListener(ProfileImageAttachSourceFragment.ATTACH_SOURCE_RESULT_KEY) { _, bundle ->
            when (bundle.getString(ProfileImageAttachSourceFragment.ATTACH_SOURCE_RESULT_KEY)) {
                ProfileImageAttachSourceFragment.SRC_GALLERY -> selectImageFromGallery()
                ProfileImageAttachSourceFragment.SRC_CAMERA -> takeImage()
            }
        }

        setFragmentResultListener(PHONE_VERIFICATION_REQUEST_KEY) { _, bundle ->
            val success = bundle.getBoolean(PHONE_VERIFICATION_RESULT_KEY)
            if (success) {
                val payoutId = businessWithDrawviewModel._backEndDbID.value
                viewModel.submitPaymentMethodForm(payoutId?:return@setFragmentResultListener,true)
            } else {
                // Optional: Show error message
                Toast.makeText(requireContext(), "OTP verification failed", Toast.LENGTH_SHORT).show()
            }
        }

        viewModel.corruptedPaymentMethodFormLiveData.observe(viewLifecycleOwner) { paymentMethodForm ->
            paymentMethodForm?.let {
                binding.container.removeAllViews()

                for (item in it.inputs) {
                    if (item.inputType == InputType.TEXT) {
                        val customViewBinding = LayoutFormTextFieldBinding.inflate(inflater, binding.container, false)
                        customViewBinding.labelTextName.text = item.inputLabel
                        customViewBinding.textInputLabelName.hint = item.inputPlaceholder
                        customViewBinding.textInputLayout.id = item.id
                        if (item.inputData !== null) {
                            customViewBinding.textInputLabelName.setText(item.inputData)
                            viewModel.updateFormDataField(item.id,item.inputData!!)
                        }
                        customViewBinding.textInputLabelName.addTextChangedListener(object : TextWatcher {
                            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}
                            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {}
                            override fun afterTextChanged(s: Editable?) {
                                viewModel.updateFormDataField(item.id, s.toString())
                                customViewBinding.textInputLayout.helperText = null
                            }
                        })
                        customViewBinding.nameInfo.setOnClickListener {
                            val balloon = createTooltipBalloon(requireContext(), item.inputInfotext)
                            balloon.showAlignBottom(customViewBinding.nameInfo)
                        }
                        binding.container.addView(customViewBinding.root)
                    } else if (item.inputType == InputType.DROP_DOWN) {
                        val customSpinnerBinding = LayoutFormSpinnerFieldBinding.inflate(inflater, binding.container, false)
                        val spinnerOptions = item.options.map { it.inputTypeValue }.toTypedArray()
                        val autoCompleteTextView = customSpinnerBinding.accountTypeAutoComplete
                        with(customSpinnerBinding) {
                            labelTextName.text = item.inputLabel
                            helperText.id = item.id
                            val adapter = ArrayAdapter(requireContext(), android.R.layout.simple_dropdown_item_1line, spinnerOptions)
                            accountTypeAutoComplete.setAdapter(adapter)
                            accountTypeAutoComplete.setOnItemClickListener { parent, view, position, id ->
                                viewModel.updateFormDataField(item.id, spinnerOptions[position])
                            }
                            spinnerInfo.setOnClickListener {
                                val balloon = createTooltipBalloon(requireContext(), item.inputInfotext)
                                balloon.showAlignBottom(spinnerInfo)
                            }
                        }

                        if (spinnerOptions.isNotEmpty()) {
                            autoCompleteTextView.setText(spinnerOptions[0], false)
                            viewModel.updateFormDataField(item.id, spinnerOptions[0])
                        }

                        binding.container.addView(customSpinnerBinding.root)
                    } else if (item.inputType == InputType.FILE) {
                        if (item.inputData !== null) {
                            viewModel.updateImageLiveData(item.inputData!!)
                        }

                        val customFileBinding: LayoutFormFileFieldBinding = DataBindingUtil.inflate(inflater, R.layout.layout_form_file_field, binding.container, false)
                        binding.lifecycleOwner = viewLifecycleOwner
                        customFileBinding.fileUploadHelperText.id = item.id
                        customFileBinding.labelPassportImage.text = item.inputLabel
                        customFileBinding.camera.setOnClickListener {
                            val action = CreateHuddleFragmentDirections.actionGlobalProfileImageAttachSourceFragment(true)
                            findNavController().navigateSafe(action)
                        }

                        customFileBinding.passportImageInfo.setOnClickListener {
                            val balloon = createTooltipBalloon(requireContext(), item.inputInfotext)
                            balloon.showAlignBottom(customFileBinding.passportImageInfo)
                        }
                        viewModel.finalImagePath.observe(viewLifecycleOwner) { imagePath ->
                            loadImage(customFileBinding.passportImage, imagePath)
                        }
                        binding.container.addView(customFileBinding.root)
                    } else if (item.inputType == InputType.PHONE) {
                        val customFormPhoneFieldBinding = LayoutFormPhoneFieldBinding.inflate(inflater, binding.container, false)
                        customFormPhoneFieldBinding.labelPhoneNumber.text = item.inputLabel
                        customFormPhoneFieldBinding.textInputPhoneNumberLayout.id = item.id
                        customFormPhoneFieldBinding.textInputPhoneNumber.hint = item.inputPlaceholder
                        
                        // Check if user is Egyptian OR if the payment method is Egyptian
                        if (args.isEgyptianPaymentMethod) {
                            // Set country code to Egypt (+20)
                            customFormPhoneFieldBinding.phoneCountryCodePicker.setCountryForNameCode(BusinessCustomerInformationFragment.EGYPT_COUNTRY_CODE_ISO)
                            viewModel.setPhoneCountryCode(customFormPhoneFieldBinding.phoneCountryCodePicker.selectedCountryCodeWithPlus)
                            customFormPhoneFieldBinding.phoneCountryCodePicker.setCcpClickable(false)
                            customFormPhoneFieldBinding.phoneCountryCodePicker.isEnabled = false
                            customFormPhoneFieldBinding.phoneCountryCodePicker.alpha = 0.5f
                            customFormPhoneFieldBinding.phoneCountryCodePicker.isFocusable = false
                            customFormPhoneFieldBinding.phoneCountryCodePicker.isClickable = false
                        } else {
                            viewModel.setPhoneCountryCode(customFormPhoneFieldBinding.phoneCountryCodePicker.selectedCountryCodeWithPlus)
                            customFormPhoneFieldBinding.phoneCountryCodePicker.alpha = 1.0f
                            customFormPhoneFieldBinding.phoneCountryCodePicker.apply {
                                setOnCountryChangeListener {
                                    viewModel.setPhoneCountryCode(selectedCountryCodeWithPlus)
                                }
                                registerCarrierNumberEditText(customFormPhoneFieldBinding.textInputPhoneNumber)
                                setPhoneNumberValidityChangeListener { valid ->
                                    viewModel.setPhoneNumberValid(valid)
                                }
                            }
                        }
                        
                        if (item.inputData !== null) {
                            viewModel.updateImageLiveData(item.inputData!!)
                            customFormPhoneFieldBinding.phoneCountryCodePicker.setCountryForPhoneCode(UserInfoUtil.getCountryCode(item.inputData!!).toInt())
                            customFormPhoneFieldBinding.textInputPhoneNumber.setText(UserInfoUtil.extractPhoneNumber(item.inputData!!))
                        }

                        customFormPhoneFieldBinding.phoneNumberInfo.setOnClickListener {
                            val balloon = createTooltipBalloon(requireContext(), item.inputInfotext)
                            balloon.showAlignBottom(customFormPhoneFieldBinding.phoneNumberInfo)
                        }

                        customFormPhoneFieldBinding.textInputPhoneNumber.addTextChangedListener(object : TextWatcher {
                            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}
                            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {}
                            override fun afterTextChanged(s: Editable?) {
                                val trimmedPhoneNumber = s?.replace("\\s+".toRegex(), "")
                                // FIXME: make phone number insert only if country code not null
                                viewModel.updateFormDataField(item.id, viewModel.phoneCountryCode.value.toString() + "-" + trimmedPhoneNumber)
                                customFormPhoneFieldBinding.textInputPhoneNumberLayout.helperText = ""
                            }
                        })
                        binding.container.addView(customFormPhoneFieldBinding.root)
                    }
                    else if (item.inputType == InputType.RADIO) {
                        val customRadioBinding: LayoutFormRadioFieldBinding = DataBindingUtil.inflate(
                            inflater, R.layout.layout_form_radio_field, binding.container, false
                        )

                        customRadioBinding.labelRadioName.text = item.inputLabel
                        customRadioBinding.helperText.id = item.id

                        val radioOptions = item.options.sortedBy { it.listingOrder }
                        val savedSelection = item.inputData
                        var isAnyOptionPreSelected = false

                        val radioButtons = radioOptions.map { option ->
                            RadioButton(requireContext()).apply {
                                this.id = option.listingOrder
                                this.text = option.inputTypeValue
                                this.layoutParams = RadioGroup.LayoutParams(
                                    RadioGroup.LayoutParams.MATCH_PARENT,
                                    RadioGroup.LayoutParams.WRAP_CONTENT
                                )
                                this.setTextAppearance(R.style.TextAppearance_Flashat_Body2)
                                this.buttonTintList = ColorStateList.valueOf(
                                    ContextCompat.getColor(requireContext(), R.color.colorPrimary)
                                )

                                if (!savedSelection.isNullOrEmpty() && option.inputTypeValue == savedSelection) {
                                    this.isChecked = true
                                    isAnyOptionPreSelected = true
                                }
                            }
                        }

                        radioButtons.forEach { radioButton ->
                            customRadioBinding.radioGroup.addView(radioButton)
                        }


                        if (!isAnyOptionPreSelected && radioOptions.isNotEmpty()) {

                            val firstRadioButton = customRadioBinding.radioGroup.getChildAt(0) as? RadioButton
                            firstRadioButton?.isChecked = true
                            viewModel.updateFormDataField(item.id, radioOptions[0].inputTypeValue)
                        } else if (isAnyOptionPreSelected && !savedSelection.isNullOrEmpty()) {
                            viewModel.updateFormDataField(item.id, savedSelection)
                        }


                        customRadioBinding.radioGroup.setOnCheckedChangeListener { group, checkedId ->
                            if (checkedId != -1) {
                                val selectedRadioButton = group.findViewById<RadioButton>(checkedId)
                                val selectedValue = selectedRadioButton.text.toString()
                                viewModel.updateFormDataField(item.id, selectedValue)
                                customRadioBinding.helperText.text = null
                            }
                        }

                        customRadioBinding.radioInfo.setOnClickListener {
                            val balloon = createTooltipBalloon(requireContext(), item.inputInfotext)
                            balloon.showAlignBottom(customRadioBinding.radioInfo)
                        }

                        binding.container.addView(customRadioBinding.root)
                    }
                }
            }
        }

    }

    private fun showError(viewId: Int, errorMessage: PayoutFormErrorType?) {
        val view = binding.container.findViewById<View>(viewId)
        if (view is TextInputLayout) {
            view.helperText = when (errorMessage) {
                PayoutFormErrorType.REQUIRED -> context?.getString(R.string.error_required)
                PayoutFormErrorType.SPECIAL_CHARACTERS -> context?.getString(R.string.special_char_error)
                PayoutFormErrorType.INVALID_PHONE_NUMBER -> context?.getString(R.string.phone_number_invalid_error)
                PayoutFormErrorType.SELECT_OPTION -> context?.getString(R.string.error_select_option)
                else -> null
            }
        } else if (view is TextView) {
            view.text = context?.getString(R.string.passport_error)
        }
    }

    private fun createTooltipBalloon(context: Context, text: String): Balloon {
        return createBalloon(context) {
            setHeight(BalloonSizeSpec.WRAP)
            setLayout(R.layout.layout_sell_flax_tool_tip)
            setMargin(16)
            setArrowPositionRules(ArrowPositionRules.ALIGN_ANCHOR)
            setCornerRadius(8f)
            setBackgroundColorResource(R.color.colorAlwaysLightSurface)
            setBalloonAnimation(BalloonAnimation.ELASTIC)
            build()
        }.apply {
            getContentView().findViewById<AppCompatTextView>(R.id.message).text = text
        }
    }
}
