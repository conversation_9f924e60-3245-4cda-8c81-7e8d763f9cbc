package com.app.messej.ui.home.businesstab

import android.graphics.Typeface
import android.os.Bundle
import android.text.BidiFormatter
import android.text.style.StyleSpan
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.setFragmentResult
import androidx.fragment.app.viewModels
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import com.app.messej.R
import com.app.messej.data.model.enums.OtpContext
import com.app.messej.databinding.FragmentPayoutOtpBinding
import com.app.messej.ui.home.businesstab.operations.PayoutOtpViewModel
import com.app.messej.ui.utils.NavigationExtensions.navigateSafe
import com.app.messej.ui.utils.TextFormatUtils.highlightOccurrences
import com.google.android.material.bottomsheet.BottomSheetDialogFragment


class PayoutOtpFragment : BottomSheetDialogFragment() {

    companion object {
        const val PHONE_VERIFICATION_REQUEST_KEY = "phone_verification_request_key"
        const val PHONE_VERIFICATION_RESULT_KEY = "phone_verification_result_key"
    }

    private lateinit var binding:FragmentPayoutOtpBinding
    private val viewModel: PayoutOtpViewModel by viewModels()
    private val args: PayoutOtpFragmentArgs by navArgs()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View? {
        // Inflate the layout for this fragment
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_payout_otp, container, false)
        binding.lifecycleOwner = viewLifecycleOwner
        binding.viewModel = viewModel
        return binding.root
    }
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setUp()
        observe()
    }
    private fun setUp() {

        //setting initial values from args
        viewModel.init(args.phoneCountryCode, args.phoneNumber, args.otpContext)
    
        val phoneNo = args.phoneCountryCode + args.phoneNumber
        val text=String.format( getString(R.string.register_otp_label_info),  BidiFormatter.getInstance().unicodeWrap(phoneNo))
        binding.infoText.text = text.highlightOccurrences(phoneNo){StyleSpan(Typeface.BOLD)}
        binding.verifyButton.setOnClickListener {
            binding.otpCodeView.otp?.takeIf { it.isNotEmpty() }?.let { otp ->
          viewModel.verifyOtp(otp)
    
            } ?: run {
                Toast.makeText(requireActivity(), "Empty OTP", Toast.LENGTH_SHORT).show()
            }
        }
        binding.changeNumberText.setOnClickListener {
            findNavController().popBackStack()
        }
    
    }
    private fun observe() {
        viewModel.isPhoneNumberVerified.observe(viewLifecycleOwner){
            if(it!=null){
                if(it){
                    if (args.otpContext == OtpContext.PAYMENT_METHOD) {
                        setFragmentResult(PHONE_VERIFICATION_REQUEST_KEY, Bundle().apply { putBoolean(PHONE_VERIFICATION_RESULT_KEY, true) })
                        findNavController().popBackStack()
                    } else {
                        findNavController().navigateSafe(PayoutOtpFragmentDirections.actionPayoutOtpFragmentToPaymentMethodsFragment())
                    }
                }

            }
        }
        viewModel.errorEvent.observe(viewLifecycleOwner){
            setFragmentResult(PHONE_VERIFICATION_REQUEST_KEY, Bundle().apply { putBoolean(PHONE_VERIFICATION_RESULT_KEY, false) })
            findNavController().popBackStack()
        }
    }
    override fun onStart() {
        super.onStart()
    }
    override fun onStop() {
        super.onStop()
    }



}